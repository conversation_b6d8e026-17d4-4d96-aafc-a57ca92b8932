import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import enhancedDocumentService from '@/lib/enhanced-document-service';
import documentManager from '@/lib/document-manager';

const UPLOAD_DIR = path.join(process.cwd(), 'uploads');

// Ensure upload directory exists
async function ensureUploadDir() {
  if (!existsSync(UPLOAD_DIR)) {
    await mkdir(UPLOAD_DIR, { recursive: true });
  }
}

export async function POST(request: NextRequest) {
  try {
    await ensureUploadDir();

    // Initialize document manager to load existing metadata
    await documentManager.initialize();

    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Validate file type
    const allowedTypes = ['.txt', '.md'];
    const fileExtension = path.extname(file.name).toLowerCase();

    if (!allowedTypes.includes(fileExtension)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only .txt and .md files are allowed.' },
        { status: 400 }
      );
    }

    // Generate unique filename
    const fileId = uuidv4();
    const fileName = `${fileId}${fileExtension}`;
    const filePath = path.join(UPLOAD_DIR, fileName);

    // Save file
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    await writeFile(filePath, buffer);

    // Add to document manager (now properly initialized)
    await documentManager.addDocument({
      id: fileId,
      filename: fileName,
      originalName: file.name,
      size: file.size,
      type: fileExtension,
    });

    // Process file with Enhanced Document Service (DeepSeek + Doubao)
    try {
      await enhancedDocumentService.addDocument(filePath, fileId, file.name, file.size);
    } catch (indexError) {
      console.error('Failed to process document with enhanced service:', indexError);
      // Continue even if processing fails - file is still uploaded
    }

    return NextResponse.json({
      id: fileId,
      name: file.name,
      size: file.size,
      path: filePath,
      message: 'File uploaded and processed with DeepSeek + Doubao successfully'
    });

  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json(
      { error: 'Failed to upload file' },
      { status: 500 }
    );
  }
}
