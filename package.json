{"name": "chatdoc-v1", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@llamaindex/env": "^0.1.30", "@llamaindex/openai": "^0.4.7", "@llamaindex/readers": "^3.1.12", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "@tanstack/react-query": "^5.81.5", "cheerio": "^1.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "formidable": "^3.5.4", "framer-motion": "^12.23.0", "highlight.js": "^11.11.1", "llamaindex": "^0.11.13", "lucide-react": "^0.525.0", "multer": "^2.0.1", "next": "15.3.5", "node-html-markdown": "^1.3.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "rehype-highlight": "^7.0.2", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.3.1", "turndown": "^7.2.0", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/formidable": "^3.4.5", "@types/multer": "^2.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5"}}