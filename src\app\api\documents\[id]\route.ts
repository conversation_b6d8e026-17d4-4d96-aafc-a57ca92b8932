import { NextRequest, NextResponse } from 'next/server';
import { unlink } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';
import enhancedDocumentService from '@/lib/enhanced-document-service';
import documentManager from '@/lib/document-manager';

const UPLOAD_DIR = path.join(process.cwd(), 'uploads');

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  let documentId: string | undefined;
  try {
    const { id } = await params;
    documentId = id;

    if (!id) {
      return NextResponse.json(
        { error: 'Document ID is required' },
        { status: 400 }
      );
    }

    console.log(`Attempting to delete document with ID: ${id}`);

    // Initialize document manager to access metadata
    await documentManager.initialize();

    // Get document metadata to find the actual filename
    const documentMetadata = documentManager.getDocument(id);

    if (!documentMetadata) {
      console.error(`Document metadata not found for ID: ${id}`);
      return NextResponse.json(
        { error: 'Document not found in metadata' },
        { status: 404 }
      );
    }

    console.log(`Found document metadata: ${JSON.stringify(documentMetadata)}`);

    // Construct file path using the actual filename from metadata
    const filePath = path.join(UPLOAD_DIR, documentMetadata.filename);

    console.log(`Checking file path: ${filePath}`);

    // Check if file exists
    if (!existsSync(filePath)) {
      console.error(`File not found on disk: ${filePath}`);

      // File doesn't exist on disk, but we should still clean up metadata
      try {
        await enhancedDocumentService.removeDocument(id);
        console.log(`Cleaned up metadata for missing file: ${id}`);
      } catch (serviceError) {
        console.error('Failed to clean up metadata for missing file:', serviceError);
      }

      return NextResponse.json(
        { error: 'Document file not found on disk' },
        { status: 404 }
      );
    }

    // Delete the file from disk
    console.log(`Deleting file: ${filePath}`);
    await unlink(filePath);
    console.log(`Successfully deleted file: ${filePath}`);

    // Remove from Enhanced Document Service (this also removes from document manager)
    try {
      console.log(`Removing document from enhanced service: ${id}`);
      await enhancedDocumentService.removeDocument(id);
      console.log(`Successfully removed document from enhanced service: ${id}`);
    } catch (serviceError) {
      console.error('Failed to remove document from enhanced service:', serviceError);
      // Continue even if service removal fails - file is still deleted
    }

    console.log(`Document deletion completed successfully: ${id}`);
    return NextResponse.json({
      message: 'Document deleted successfully'
    });

  } catch (error) {
    console.error(`Delete error for document ${documentId || 'unknown'}:`, error);
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
    return NextResponse.json(
      { error: 'Failed to delete document' },
      { status: 500 }
    );
  }
}
